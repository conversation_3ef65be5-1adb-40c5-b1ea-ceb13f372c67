import React from 'react';
import { FONTS } from '@constants/fonts';
import { View, StyleSheet } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import CardButton from '@/partials/card-button';
import CardShadowButton from '@/partials/card-shadow-button';
import { BlurView } from '@react-native-community/blur';
import { StatusBar } from 'react-native';
import ControlButtons from '@src/components/ControlButtons';
import SettingsModal from '@src/components/SettingsModal';
import BlurBottomSheetExample from '@src/components/LanguageBS';

export default function GamePlay() {
  const insets = useSafeAreaInsets();

  const [isSettingsModalOpen, setIsSettingsModalOpen] = React.useState(false);

  return (
    <>
      <StatusBar barStyle="light-content" />
      <BlurView
        blurType="light"
        blurAmount={10}
        reducedTransparencyFallbackColor="dark"
        style={[styles.blurView]}
      >
        <LinearGradient
          colors={['rgba(79, 26, 6, 0.9)']}
          locations={[1]}
          style={styles.linearContainer}
        >
          <View
            style={[
              styles.container,
              { paddingBottom: insets.bottom, paddingTop: insets.top },
            ]}
          >
            <View style={styles.icons}>
              <ControlButtons
                onOpenSettings={() => {
                  setIsSettingsModalOpen(true);
                }}
              />
            </View>

            <View style={styles.termsContainer}>
              <CardShadowButton
                title="Shuffle"
                onPress={() => {}}
                containerStyle={styles.hidden}
              />
              <CardButton
                title="Shuffle"
                containerStyle={{ width: 'auto' }}
                onPress={() => {}}
              />
              <CardShadowButton
                title="Shuffle"
                onPress={() => {}}
                containerStyle={{ width: 'auto' }}
              />
            </View>
          </View>
        </LinearGradient>
        <SettingsModal
          visible={isSettingsModalOpen}
          onClose={() => {
            setIsSettingsModalOpen(false);
          }}
        />
        <BlurBottomSheetExample />
      </BlurView>
    </>
  );
}

const styles = StyleSheet.create({
  blurView: {
    flex: 1,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    overflow: 'hidden',
  },
  linearContainer: {
    flex: 1,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  container: {
    flex: 1,
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  closeButtonContainer: {
    width: '100%',
    alignItems: 'flex-end',
    padding: 16,
  },
  button: {
    backgroundColor: '#2196F3',
    padding: 15,
    borderRadius: 10,
    alignItems: 'center',
    marginBottom: 10,
  },
  closeButton: {
    backgroundColor: '#FF5252',
    marginTop: 20,
  },
  buttonText: {
    color: 'white',
    fontWeight: 'bold',
  },
  title: {
    fontSize: 40,
    fontFamily: FONTS.IMBUE.BOLD,
    color: 'rgba(255, 255, 255, 0.85)',
    textTransform: 'uppercase',
    textAlign: 'center',
  },
  descriptionContainer: {
    marginBottom: 16,
  },
  description: {
    fontSize: 16,
    fontFamily: FONTS.BALOO2.REGULAR,
    color: 'rgba(255, 255, 255, 0.85)',
    opacity: 0.7,
    textAlign: 'center',
  },
  imageContainer: {
    alignItems: 'center',
  },
  image: {
    aspectRatio: 14 / 10,
    width: '90%',
    height: 150,
  },
  infoLabel: {
    fontSize: 16,
    fontFamily: FONTS.BALOO2.REGULAR,
    textAlign: 'center',
    color: 'rgba(255, 255, 255, 0.85)',
  },
  freeTrial: {
    fontSize: 24,
    fontFamily: FONTS.IMBUE.MEDIUM,
    textAlign: 'center',
    color: 'rgba(255, 255, 255, 0.85)',
  },
  termsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    textAlign: 'center',
    gap: 20,
  },
  termsLabel: {
    fontSize: 14,
    fontFamily: FONTS.BALOO2.REGULAR,
    color: '#8E7BA1',
    lineHeight: 20,
  },
  buttonContainer: {
    width: '100%',
    gap: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  contentIcon: {
    width: 16,
    height: 16,
    marginRight: 16,
  },
  infoContainer: {
    gap: 8,
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  hidden: {
    width: 'auto',
    opacity: 0,
  },
  icons: {
    width: '100%',
  },
});
