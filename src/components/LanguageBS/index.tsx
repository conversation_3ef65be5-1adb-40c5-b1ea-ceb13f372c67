import React, { useCallback, useMemo, useRef, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Platform,
} from 'react-native';
import BottomSheet, { 
  BottomSheetView, 
  BottomSheetBackdrop,
  BottomSheetScrollView,
  BottomSheetBackdropProps
} from '@gorhom/bottom-sheet';
import { BlurView } from '@react-native-community/blur';

// Types
interface CardData {
  id: number;
  title: string;
  content: string;
}

interface BottomSheetItem {
  id: number;
  title: string;
  description: string;
}

type BlurType = 'light' | 'dark' | 'xlight' | 'prominent';

interface BlurBottomSheetProps {
  initialSnapIndex?: number;
  snapPoints?: string[];
  blurType?: BlurType;
  blurAmount?: number;
}

const BlurBottomSheetExample: React.FC<BlurBottomSheetProps> = ({
  initialSnapIndex = -1,
  snapPoints: customSnapPoints,
  blurType = 'light',
  blurAmount = 15
}) => {
  const bottomSheetRef = useRef<BottomSheet>(null);
  const [isOpen, setIsOpen] = useState<boolean>(false);

  // Snap points cho bottom sheet
  const snapPoints = useMemo(() => customSnapPoints || ['25%'], [customSnapPoints]);

  const bottomSheetItems: BottomSheetItem[] = useMemo(() => [
    {
      id: 1,
      title: 'Option 1',
      description: 'Mô tả cho option 1'
    },
    {
      id: 2,
      title: 'Option 2',
      description: 'Mô tả cho option 2'
    },
    {
      id: 3,
      title: 'Option 3',
      description: 'Mô tả cho option 3'
    }
  ], []);

  // Hàm mở bottom sheet
  const handleOpenBottomSheet = useCallback((): void => {
    bottomSheetRef.current?.snapToIndex(1);
    setIsOpen(true);
  }, []);

  // Hàm đóng bottom sheet
  const handleCloseBottomSheet = useCallback((): void => {
    bottomSheetRef.current?.close();
    setIsOpen(false);
  }, []);

  // Handle sheet changes
  const handleSheetChanges = useCallback((index: number): void => {
    setIsOpen(index >= 0);
  }, []);

  // Custom backdrop với blur effect
  const renderBackdrop = useCallback(
    (props: BottomSheetBackdropProps) => (
      <BottomSheetBackdrop
        {...props}
        disappearsOnIndex={-1}
        appearsOnIndex={0}
        opacity={0.5}
        pressBehavior="close"
      />
    ),
    []
  );

  // Custom backdrop với BlurView (iOS)
  const renderBlurBackdrop = useCallback(
    (props: BottomSheetBackdropProps) => {
      if (Platform.OS === 'ios') {
        return (
          <View style={[StyleSheet.absoluteFillObject, { zIndex: 1 }]}>
            <BlurView
              style={StyleSheet.absoluteFillObject}
              blurType={blurType}
              blurAmount={blurAmount}
              reducedTransparencyFallbackColor="rgba(0,0,0,0.5)"
            />
          </View>
        );
      }
      
      // Fallback cho Android
      return (
        <BottomSheetBackdrop
          {...props}
          disappearsOnIndex={-1}
          appearsOnIndex={0}
          opacity={0.7}
          pressBehavior="close"
        />
      );
    },
    [blurType, blurAmount]
  );


  // Render bottom sheet item
  const renderBottomSheetItem = useCallback(({ item }: { item: BottomSheetItem }) => (
    <TouchableOpacity 
      key={item.id} 
      style={styles.bottomSheetItem}
      onPress={() => console.log(`Selected: ${item.title}`)}
    >
      <Text style={styles.itemTitle}>{item.title}</Text>
      <Text style={styles.itemDescription}>{item.description}</Text>
    </TouchableOpacity>
  ), []);

  return (
    <>
      {/* Content phía trên */}

      {/* Bottom Sheet với blur backdrop */}
      <BottomSheet
        ref={bottomSheetRef}
        index={initialSnapIndex}
        snapPoints={snapPoints}
        backdropComponent={renderBackdrop}
        enablePanDownToClose={true}
        onChange={handleSheetChanges}
        backgroundStyle={styles.bottomSheetBackground}
        handleIndicatorStyle={styles.handleIndicator}
        animateOnMount={true}
      >
        <BottomSheetView style={styles.bottomSheetContent}>
          <Text style={styles.bottomSheetTitle}>Bottom Sheet Content</Text>
          
          <TouchableOpacity 
            style={styles.closeButton}
            onPress={handleCloseBottomSheet}
            activeOpacity={0.7}
          >
            <Text style={styles.closeButtonText}>Đóng</Text>
          </TouchableOpacity>

          <BottomSheetScrollView 
            style={styles.scrollContent}
            showsVerticalScrollIndicator={false}
          >
            {bottomSheetItems.map((item) => renderBottomSheetItem({ item }))}
          </BottomSheetScrollView>
        </BottomSheetView>
      </BottomSheet>

      {/* Blur overlay tùy chỉnh cho iOS */}
      {Platform.OS === 'ios' && isOpen && (
        <BlurView
          style={[StyleSheet.absoluteFillObject, styles.blurOverlay]}
          blurType={blurType}
          blurAmount={blurAmount}
          reducedTransparencyFallbackColor="rgba(255,255,255,0.8)"
        />
      )}

      {/* Blur overlay cho Android */}
      {Platform.OS === 'android' && isOpen && (
        <View style={[StyleSheet.absoluteFillObject, styles.androidBlurOverlay]} />
      )}
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
    color: '#333',
  },
  
  // Cards
  card: {
    backgroundColor: 'white',
    padding: 20,
    borderRadius: 12,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 8,
    color: '#333',
  },
  cardContent: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  
  // Open button
  openButton: {
    backgroundColor: '#007AFF',
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    marginTop: 20,
  },
  openButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  
  spacer: {
    height: 100,
  },
  
  // Bottom sheet styles
  bottomSheetBackground: {
    backgroundColor: 'white',
    borderRadius: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8,
  },
  handleIndicator: {
    backgroundColor: '#E0E0E0',
    width: 40,
    height: 4,
  },
  bottomSheetContent: {
    flex: 1,
    padding: 20,
  },
  bottomSheetTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
    color: '#333',
  },
  
  // Close button
  closeButton: {
    backgroundColor: '#FF3B30',
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 20,
  },
  closeButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  
  // Scroll content
  scrollContent: {
    flex: 1,
  },
  
  // Bottom sheet items
  bottomSheetItem: {
    paddingVertical: 16,
    paddingHorizontal: 4,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  itemTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    marginBottom: 4,
  },
  itemDescription: {
    fontSize: 14,
    color: '#666',
  },
  
  // Blur overlay
  blurOverlay: {
    zIndex: 1,
    pointerEvents: 'none',
  },
  androidBlurOverlay: {
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    zIndex: 1,
    pointerEvents: 'none',
  },
});

export default BlurBottomSheetExample;

// Export types for reuse
export type { BlurBottomSheetProps, CardData, BottomSheetItem, BlurType };