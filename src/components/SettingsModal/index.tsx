import { FONTS } from '@constants/fonts';
import { BlurView } from '@react-native-community/blur';
import AmericaFlagIcon from '@src/icons/AmericaFlagIcon';
import ArrowRightIcon from '@src/icons/ArrowRightIcon';
import XIcon from '@src/icons/XIcon';
import React from 'react';
import {
  View,
  StyleSheet,
  Text,
  Modal,
  Dimensions,
  Switch,
  Pressable,
} from 'react-native';

const { width, height } = Dimensions.get('window');

const SettingsModal = ({
  visible = false,
  onClose = () => {},
}: {
  visible?: boolean;
  backgroundColor?: string;
  text?: string;
  onClose?: () => void;
}) => {
  return (
    <Modal
      id="settings-modal"
      transparent={true}
      animationType="fade"
      visible={visible}
      onRequestClose={() => {}}
      style={styles.overlay}
    >
      <BlurView
        blurType="dark"
        blurAmount={30}
        reducedTransparencyFallbackColor="white"
        style={[styles.blurView]}
      >
        <View style={[styles.contentContainer]}>
          <View style={styles.container}>
            <View style={styles.closeButtonContainer}>
              <Pressable onPress={onClose} style={styles.closeButtonContainer}>
                <XIcon width={24} height={24} />
              </Pressable>
            </View>
            <Text style={styles.title}>Settings</Text>
            <View style={styles.labelContainer}>
              <View style={styles.labelItemContainer}>
                <View style={styles.labelItemContainer}>
                  <Text style={[styles.label]}>Languages </Text>
                  <ArrowRightIcon width={12} height={12} />
                </View>
                <View style={styles.languageContainer}>
                  <AmericaFlagIcon width={24} height={24} />
                  <Text style={[styles.label]}>Eng </Text>
                </View>
              </View>
              <View style={styles.labelItemContainer}>
                <Text style={styles.label}>Sound effect</Text>
                <Switch value={false} />
              </View>
              <View style={styles.labelItemContainer}>
                <Text style={styles.label}>Turn base</Text>
                <Switch value={true} />
              </View>
            </View>
          </View>
        </View>
      </BlurView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    justifyContent: 'center',
    alignItems: 'center',
    width: width,
    height: height,
    position: 'relative',
  },
  closeButtonContainer: {
    position: 'absolute',
    top: 6,
    right: 10,
    padding: 8,
    zIndex: 1,
  },
  container: {
    backgroundColor: '#0B1713B2',
    paddingHorizontal: 24,
    paddingVertical: 32,
    borderRadius: 40,
    position: 'relative',
    gap: 24,
  },
  spinner: {
    marginBottom: 15,
  },
  text: {
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',
  },
  blurView: {
    flex: 1,
  },
  backgroundStyle: {
    backgroundColor: 'transparent',
  },
  contentContainer: {
    flex: 1,
    justifyContent: 'center',
    paddingHorizontal: 24,
  },
  title: {
    fontSize: 32,
    fontFamily: FONTS.BALOO2.EXTRA_BOLD,
    color: 'rgba(255, 255, 255)',
  },
  labelContainer: {
    gap: 24,
  },
  label: {
    fontSize: 16,
    fontFamily: FONTS.BALOO2.REGULAR,
    color: 'rgba(255, 255, 255, 0.85)',
    lineHeight: 24,
  },
  labelItemContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  languageContainer: {
    flexDirection: 'row',
    gap: 8,
  },
});

export default SettingsModal;
