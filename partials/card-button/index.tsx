import { Pressable, StyleProp, StyleSheet, Text, View, ViewStyle } from "react-native";

interface ButtonProps {
  title: string;
  onPress: () => void;
  containerStyle?: StyleProp<ViewStyle>;
}

function CardButton( {
  title,
  onPress,
  containerStyle,
} : ButtonProps) {
  return (
    <Pressable onPress={onPress} style={[style.buttonContainer, containerStyle]}>
      <View style={style.container}>
        <Text style={style.text}>{title}</Text>
      </View>
    </Pressable>
  );
};

const style = StyleSheet.create({
  buttonContainer: {
    width: '100%',
    maxWidth: 220,
    height: 48,
  },
  container: {
    width: '100%',
    backgroundColor: '#1B1B1B',
    padding: 16,
    borderRadius: 60,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#444',
    textAlign: 'center',
  },
  text: {
    color: '#F5F5F5',
    fontWeight: 400,
    lineHeight: 16,
    fontSize: 16,
  },
});

export default CardButton;

